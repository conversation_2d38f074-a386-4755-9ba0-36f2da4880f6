import { model } from "@camped-ai/framework/utils";
import { Destination } from "./destination";

export const DestinationFaq = model
  .define(
    { tableName: "destination_faq", name: "DestinationFaq" },
    {
      id: model.id({ prefix: "dest_faq" }).primaryKey(),
      question: model.text(),
      answer: model.text(),
      rank: model.number().default(0),
      destination_id: model.text(),
      destination: model.belongsTo(() => Destination, {
        mappedBy: "faqs",
      }),
    }
  )
  .indexes([
    {
      name: "IDX_destination_faq_destination_id",
      on: ["destination_id"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_destination_faq_rank",
      on: ["rank"],
      unique: false,
      where: "deleted_at IS NULL",
    },
  ]);
