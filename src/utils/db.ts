// db.ts
import { Pool, PoolClient } from 'pg';

// Configuration interface for the database pool
interface DbPoolConfig {
  connectionString?: string;
  host?: string;
  port?: number;
  database?: string;
  user?: string;
  password?: string;
  max?: number;
  idleTimeoutMillis?: number;
  connectionTimeoutMillis?: number;
  ssl?: boolean | { rejectUnauthorized: boolean };
}

// Default configuration
const defaultConfig: DbPoolConfig = {
  max: 20, // Maximum number of clients in the pool
  idleTimeoutMillis: 30000, // How long a client is allowed to remain idle before being closed
  connectionTimeoutMillis: 2000, // How long to wait for a connection to become available
};

// Singleton pool instance
let poolInstance: Pool | null = null;

/**
 * Get the database pool instance
 * @param config Optional configuration to override defaults
 * @returns The database pool instance
 */
export function getPool(config: DbPoolConfig = {}): Pool {
  if (!poolInstance) {
    console.log("Creating new database connection pool");

    const connectionString = config.connectionString || process.env.DATABASE_URL;

    if (connectionString) {
      poolInstance = new Pool({
        connectionString,
        max: config.max || defaultConfig.max,
        idleTimeoutMillis: config.idleTimeoutMillis || defaultConfig.idleTimeoutMillis,
        connectionTimeoutMillis: config.connectionTimeoutMillis || defaultConfig.connectionTimeoutMillis,
        ssl: config.ssl,
      });
    } else {
      // Use individual connection parameters if no connection string is provided
      poolInstance = new Pool({
        host: config.host || process.env.DB_HOST || 'localhost',
        port: config.port || parseInt(process.env.DB_PORT || '5432'),
        database: config.database || process.env.DB_NAME || 'medusa',
        user: config.user || process.env.DB_USERNAME || 'postgres',
        password: config.password || process.env.DB_PASSWORD || 'postgres',
        max: config.max || defaultConfig.max,
        idleTimeoutMillis: config.idleTimeoutMillis || defaultConfig.idleTimeoutMillis,
        connectionTimeoutMillis: config.connectionTimeoutMillis || defaultConfig.connectionTimeoutMillis,
        ssl: config.ssl,
      });
    }

    // Add error handler to the pool
    poolInstance.on('error', (err: Error) => {
      console.error('Unexpected error on idle database client', err);
    });

    // Log pool creation
    console.log(`Database pool created with max ${poolInstance.options.max} connections`);
  }

  return poolInstance;
}

/**
 * Execute a query using a client from the pool
 * @param callback Function that receives a client and executes queries
 * @returns The result of the callback function
 */
export async function withClient<T>(callback: (client: PoolClient) => Promise<T>): Promise<T> {
  const client = await getPool().connect();
  try {
    return await callback(client);
  } finally {
    client.release();
  }
}

/**
 * Execute a query within a transaction
 * @param callback Function that receives a client and executes queries within a transaction
 * @returns The result of the callback function
 */
export async function withTransaction<T>(callback: (client: PoolClient) => Promise<T>): Promise<T> {
  return withClient(async (client) => {
    try {
      await client.query('BEGIN');
      const result = await callback(client);
      await client.query('COMMIT');
      return result;
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    }
  });
}

/**
 * Close the pool and all connections
 */
export async function closePool(): Promise<void> {
  if (poolInstance) {
    await poolInstance.end();
    poolInstance = null;
    console.log('Database pool has been closed');
  }
}

// For backward compatibility
const pool = getPool();
export default pool;