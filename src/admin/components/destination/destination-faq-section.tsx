import { Button, Input, Text, Textarea, IconButton } from "@camped-ai/ui";
import { useFieldArray, UseFormReturn } from "react-hook-form";
import { Plus, Trash2, HelpCircle } from "lucide-react";
import { DestinationFormData } from "../destination-form";

const DestinationFaqSection = ({
  form,
}: {
  form: UseFormReturn<DestinationFormData>;
}) => {
  const { fields, append, remove } = useFieldArray({
    name: "faqs",
    control: form.control,
    keyName: "field_id",
  });

  const addFaq = () => {
    append({
      question: "",
      answer: "",
    });
  };

  const removeFaq = (index: number) => {
    remove(index);
  };

  return (
    <div id="faqs" className="flex flex-col gap-y-4">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <HelpCircle className="w-5 h-5 text-gray-600" />
          <Text className="font-medium">Frequently Asked Questions</Text>
        </div>
        <Button
          type="button"
          variant="secondary"
          size="small"
          onClick={addFaq}
          className="flex items-center gap-2"
        >
          <Plus className="w-4 h-4" />
          Add FAQ
        </Button>
      </div>

      {fields.length === 0 ? (
        <div className="text-center py-8 border-2 border-dashed border-gray-200 rounded-lg">
          <HelpCircle className="w-12 h-12 text-gray-400 mx-auto mb-2" />
          <Text className="text-gray-500 mb-4">No FAQs added yet</Text>
          <Button
            type="button"
            variant="secondary"
            size="small"
            onClick={addFaq}
            className="flex items-center gap-2 mx-auto"
          >
            <Plus className="w-4 h-4" />
            Add Your First FAQ
          </Button>
        </div>
      ) : (
        <div className="space-y-4">
          {fields.map((field, index) => (
            <div
              key={field.field_id}
              className="border border-gray-200 rounded-lg p-4 bg-gray-50"
            >
              <div className="flex justify-between items-start mb-3">
                <Text className="font-medium text-sm text-gray-700">
                  FAQ #{index + 1}
                </Text>
                <IconButton
                  type="button"
                  variant="transparent"
                  size="small"
                  onClick={() => removeFaq(index)}
                  className="text-red-500 hover:text-red-700"
                >
                  <Trash2 className="w-4 h-4" />
                </IconButton>
              </div>

              <div className="space-y-3">
                <div>
                  <Text className="text-sm font-medium text-gray-700 mb-1">
                    Question
                  </Text>
                  <Input
                    {...form.register(`faqs.${index}.question`)}
                    placeholder="Enter the frequently asked question..."
                    className="w-full"
                  />
                </div>

                <div>
                  <Text className="text-sm font-medium text-gray-700 mb-1">
                    Answer
                  </Text>
                  <Textarea
                    {...form.register(`faqs.${index}.answer`)}
                    placeholder="Enter the answer to this question..."
                    rows={3}
                    className="w-full"
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default DestinationFaqSection;
