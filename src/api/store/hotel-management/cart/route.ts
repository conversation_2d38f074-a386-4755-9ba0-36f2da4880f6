import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import {
  createCartWorkflow,
  completeCartWorkflow,
} from "@camped-ai/medusa/core-flows";
import { Modules } from "@camped-ai/framework/utils";

// Import helper functions
import { checkRoomAvailability as checkRoomAvailabilityHelper } from "../../../admin/hotel-management/bookings/helpers";
import { RoomInventoryStatus } from "../../../../modules/hotel-management/room-inventory/models/room-inventory";

// Import validation schemas
import { StoreCartCreationSchema, type TravelersMetadataType } from "./schemas";

/**
 * Store API endpoint for creating a hotel booking cart
 * This follows the standard e-commerce flow:
 * 1. Create cart
 * 2. Add items to cart
 * 3. Set customer information
 * 4. Set shipping/billing addresses
 * 5. Return cart for payment processing
 */
export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    console.log("Creating hotel booking cart using workflow approach");

    // Check if req.scope exists
    if (!req.scope) {
      console.error(
        "req.scope is undefined in cart creation. This might be due to a middleware issue."
      );
      return res.status(400).json({
        message: "Scope is not available, cannot create cart",
      });
    }

    // Log the request body for debugging
    console.log("Request body:", req.body);

    // Validate request body using schema
    const validationResult = StoreCartCreationSchema.safeParse(req.body);

    if (!validationResult.success) {
      console.error("Validation errors:", validationResult.error.format());
      return res.status(400).json({
        message: "Invalid request body",
        errors: validationResult.error.format(),
      });
    }

    const validatedData = validationResult.data;

    // Get customer ID from session if available
    const sessionCustomerId = req.session?.customer_id;
    console.log(`Customer ID from session: ${sessionCustomerId || "none"}`);

    // Extract booking data from validated request body
    const {
      hotel_id,
      room_id,
      room_config_id,
      room_type,
      check_in_date,
      check_out_date,
      check_in_time,
      check_out_time,
      guest_name,
      guest_email,
      guest_phone,
      adults,
      children,
      infants,
      travelers, // New field for multiple guest details
      number_of_rooms,
      total_amount,
      currency_code,
      region_id,
      country_code,
      special_requests,
      notes,
      metadata,
      shipping_address,
      billing_address,
      customer_id: provided_customer_id,
      sales_channel_id,
    } = validatedData;

    // Use customer ID from session or provided in request
    const customer_id = sessionCustomerId || provided_customer_id;

    // Process travelers information - create default travelers if not provided
    let processedTravelers: TravelersMetadataType;
    if (travelers) {
      // When travelers are provided, guest_name/guest_email represents one adult
      // So we need to add the primary guest to the travelers list
      processedTravelers = {
        adults: [
          // Add the primary guest as the first adult
          { name: guest_name || "Guest" },
          // Add the additional adults from travelers
          ...travelers.adults,
        ],
        children: travelers.children,
        infants: travelers.infants,
      };
    } else {
      // Create default travelers based on counts for backward compatibility
      processedTravelers = {
        adults: Array(adults)
          .fill({})
          .map((_, index) => ({ name: index === 0 ? guest_name || "" : "" })),
        children: Array(children)
          .fill({})
          .map(() => ({ name: "", age: 0 })),
        infants: Array(infants)
          .fill({})
          .map(() => ({ name: "", age: 0 })),
      };
    }

    // Log extracted values for debugging
    console.log("Extracted values:", {
      hotel_id,
      room_id,
      check_in_date,
      check_out_date,
      guest_name,
      guest_email,
      adults,
      children,
      infants,
      total_guests: adults + children + infants,
      number_of_rooms,
      total_amount,
      currency_code,
      region_id,
      country_code,
      customer_id,
      travelers_count: {
        adults: processedTravelers.adults.length,
        children: processedTravelers.children.length,
        infants: processedTravelers.infants.length,
      },
    });

    // Additional validation for region_id format
    const regionIdStr = String(region_id);
    if (!regionIdStr.startsWith("reg_")) {
      console.error(
        `Invalid region_id format: ${regionIdStr}. It should start with 'reg_'`
      );
      return res.status(400).json({
        message: `Invalid region_id format: ${regionIdStr}. It should start with 'reg_'`,
      });
    }

    // Parse dates
    const checkInDate = new Date(check_in_date);
    const checkOutDate = new Date(check_out_date);

    // STEP 1: Check room availability
    if (room_id) {
      const availability = await checkRoomAvailabilityHelper(
        room_id,
        checkInDate,
        checkOutDate
      );

      if (!availability.available) {
        return res.status(400).json({
          message: "Room is not available for the selected dates",
          unavailable_dates: availability.unavailableDates,
        });
      }
    }

    // STEP 2: Prepare customer data
    // Split guest name into first and last name for customer data if needed
    // Use the first adult's name if guest_name is not provided
    const primaryGuestName =
      guest_name ||
      (processedTravelers.adults.length > 0
        ? processedTravelers.adults[0].name
        : "Guest");
    const nameParts = primaryGuestName
      ? primaryGuestName.split(" ")
      : ["Guest", ""];
    const firstName = nameParts[0] || "Guest";
    const lastName = nameParts.slice(1).join(" ") || "";

    // Create a default address object for use in cart creation
    const defaultAddressObj = {
      first_name: firstName,
      last_name: lastName,
      address_1: "Default Address",
      city: "Default City",
      country_code: country_code?.toLowerCase() || "us", // Use country_code from request body if provided
      postal_code: "00000",
      phone: guest_phone || "",
    };

    // Fetch room configuration name for metadata
    let roomConfigName = room_type || "Standard Room";
    if (room_config_id) {
      try {
        // Get the product that represents the room configuration
        const productService = req.scope.resolve(Modules.PRODUCT);
        const roomConfig = await productService.retrieveProduct(room_config_id);
        if (roomConfig && roomConfig.title) {
          roomConfigName = roomConfig.title;
          console.log(
            `Found room configuration for metadata: ${roomConfigName}`
          );
        }
      } catch (error) {
        console.error(
          `Error fetching room configuration name for metadata: ${error.message}`
        );
        // Continue with default name if there's an error
      }
    }

    // Fetch hotel name for metadata
    let hotelName = "Unknown Hotel";
    if (hotel_id) {
      try {
        // Get the hotel using query tool
        const query = req.scope.resolve("query");
        const { data: hotels } = await query.graph({
          entity: "hotel",
          filters: {
            id: hotel_id,
          },
          fields: ["id", "name", "title"],
        });

        if (hotels && hotels.length > 0) {
          // Use name property or fallback to default
          hotelName = hotels[0].name || hotelName;
          console.log(`Found hotel for metadata: ${hotelName}`);
        }
      } catch (error) {
        console.error(
          `Error fetching hotel name for metadata: ${error.message}`
        );
        // Continue with default name if there's an error
      }
    }

    // STEP 3: Create booking metadata
    const bookingMetadata = {
      hotel_id,
      hotel_name: hotelName, // Include hotel name in metadata
      room_config_id,
      room_id,
      room_config_name: roomConfigName, // Use room_config_name instead of room_type
      check_in_date: checkInDate.toISOString(),
      check_out_date: checkOutDate.toISOString(),
      check_in_time: check_in_time || "12:00",
      check_out_time: check_out_time || "12:00",
      guest_name,
      guest_email,
      guest_phone,
      number_of_guests: adults + children + infants,
      number_of_rooms,
      adults,
      children,
      infants,
      total_amount,
      currency_code,
      special_requests,
      notes,
      // Include travelers information in metadata
      travelers: processedTravelers,
      ...metadata,
    };

    try {
      // STEP 4: Create cart using workflow
      console.log("Creating cart using workflow...");

      // Ensure region_id is a string and properly formatted
      const regionIdStr = String(region_id);
      console.log(`Using region_id: ${regionIdStr} for cart creation`);

      // Determine which country code to use
      const usedCountryCode =
        country_code ||
        (shipping_address && shipping_address.country_code) ||
        "us";

      console.log(
        `Using country_code: ${usedCountryCode} for cart creation${
          country_code
            ? " (from request body)"
            : shipping_address?.country_code
            ? " (from shipping address)"
            : " (default)"
        }`
      );

      // Prepare cart input
      const cartInput = {
        region_id: regionIdStr,
        country_code: usedCountryCode,
        sales_channel_id: sales_channel_id,
        customer_id: customer_id,
        email: guest_email,
        currency_code: currency_code, // Add currency_code to cart input
        metadata: bookingMetadata,
        shipping_address: shipping_address || defaultAddressObj,
        billing_address:
          billing_address || shipping_address || defaultAddressObj,
      };

      // If no customer_id is provided, add customer details
      if (!customer_id && primaryGuestName) {
        Object.assign(cartInput, {
          customer: {
            email: guest_email,
            first_name: firstName,
            last_name: lastName,
            phone: guest_phone,
            metadata: {
              source: "hotel_booking",
            },
          },
        });
      }

      console.log("Cart input:", JSON.stringify(cartInput, null, 2));

      try {
        // Create the cart
        const { result: cartResult } = await createCartWorkflow(req.scope).run({
          input: cartInput,
        });

        console.log("Cart created:", cartResult);

        if (!cartResult || !cartResult.id) {
          throw new Error("Failed to create cart: Cart result is invalid");
        }

        const cartId = cartResult.id;

        // STEP 5: Add line item to cart and reserve the room temporarily
        try {
          // Resolve the cart service
          const cartService = req.scope.resolve(Modules.CART);

          // Fetch room configuration name
          let roomConfigName = "Standard Room";
          if (room_config_id) {
            try {
              // Get the product that represents the room configuration
              const productService = req.scope.resolve(Modules.PRODUCT);
              const roomConfig = await productService.retrieveProduct(
                room_config_id
              );
              if (roomConfig && roomConfig.title) {
                roomConfigName = roomConfig.title;
                console.log(`Found room configuration: ${roomConfigName}`);
              }
            } catch (error) {
              console.error(
                `Error fetching room configuration name: ${error.message}`
              );
              // Continue with default name if there's an error
            }
          }

          // Fetch hotel name for line item
          let hotelName = "Unknown Hotel";
          if (hotel_id) {
            try {
              // Get the hotel using query tool
              const query = req.scope.resolve("query");
              const { data: hotels } = await query.graph({
                entity: "hotel",
                filters: {
                  id: hotel_id,
                },
                fields: ["id", "name"],
              });

              if (hotels && hotels.length > 0) {
                hotelName = hotels[0].name || hotelName;
                console.log(`Found hotel for line item: ${hotelName}`);
              }
            } catch (error) {
              console.error(
                `Error fetching hotel name for line item: ${error.message}`
              );
              // Continue with default name if there's an error
            }
          }

          // Add the room to the cart
          await cartService.addLineItems(cartId, [
            {
              title: `Room booking: ${roomConfigName}`,
              variant_id: room_id || room_config_id,
              quantity: number_of_rooms, // Use number_of_rooms as quantity
              unit_price: Math.round(total_amount / number_of_rooms || 100), // Divide total by number of rooms for unit price
              metadata: {
                hotel_id,
                hotel_name: hotelName, // Include hotel name in line item metadata
                room_id,
                room_config_id,
                check_in_date: checkInDate.toISOString(),
                check_out_date: checkOutDate.toISOString(),
                room_config_name: roomConfigName, // Use room_config_name instead of room_type
                number_of_rooms,
              },
              requires_shipping: false,
            },
          ]);
          let reservations: any;
          // Reserve the room temporarily in the inventory
          if (room_config_id) {
            // Find and reserve an available room for this configuration
            console.log(
              "Finding and reserving available room for configuration..."
            );

            // Resolve the room inventory service
            const roomInventoryService = req.scope.resolve(
              "roomInventoryService"
            );

            // Get all variants for this room configuration
            const productService = req.scope.resolve(Modules.PRODUCT);
            const roomConfig = await productService.retrieveProduct(
              room_config_id,
              {
                relations: ["variants"],
              }
            );

            if (
              roomConfig &&
              roomConfig.variants &&
              roomConfig.variants.length > 0
            ) {
              // Check each variant for availability
              for (const variant of roomConfig.variants) {
                const availability =
                  await roomInventoryService.checkAvailability(
                    variant.id,
                    checkInDate,
                    checkOutDate
                  );
                console.log(
                  "before reserving room",
                  variant.id,
                  checkInDate,
                  checkOutDate
                );

                if (availability.available) {
                  // Reserve this room and set cart_id
                  const updatedEntries =
                    await roomInventoryService.updateInventoryStatus(
                      variant.id,
                      checkInDate,
                      checkOutDate,
                      "cart_reserved",
                      `Cart created (store): ${
                        primaryGuestName || "Guest"
                      } | Cart ID: ${
                        cartResult.id
                      } | Room Config: ${room_config_id}`,
                      null, // bookingInfo
                      null, // expiresAt
                      null, // order_id
                      cartResult.id // Set the cart_id
                    );

                  console.log(
                    "Room reservation updatedEntries:",
                    updatedEntries
                  );

                  // Populate reservations from updatedEntries data
                  reservations = updatedEntries.map((entry) => {
                    return {
                      room_id: entry.inventory_item_id,
                      from_date: new Date(entry.from_date),
                      to_date: new Date(entry.to_date),
                      status: entry.status,
                      id: entry.id,
                    };
                  });

                  break; // Found an available room, no need to check more
                }
              }
            }

            console.log("Room reservation result:", reservations);
          }
          // Update booking metadata with reservation info if available
          if (reservations) {
            const updatedMetadata = {
              ...cartResult.metadata,
              reservations: reservations,
            };

            // Update the booking metadata
            cartResult.metadata = updatedMetadata;

            // Get the order service

            // Update the order with the reservation metadata
            await cartService.updateCarts(cartResult.id, {
              metadata: updatedMetadata,
            });
          }
          // Retrieve the updated cart
          const updatedCart = await cartService.retrieveCart(cartId);

          return res.json({
            cart: updatedCart,
          });
        } catch (lineItemError) {
          // Log error for server-side debugging
          console.error("Error adding line item to cart:", lineItemError);

          // Try to retrieve the cart even if adding the line item failed
          try {
            const cartService = req.scope.resolve(Modules.CART);
            const cart = await cartService.retrieveCart(cartId);

            return res.json({
              cart,
              warning:
                "Cart created but line item could not be added. Please check the cart contents.",
            });
          } catch (retrieveError) {
            // If we can't even retrieve the cart, return the original cart result
            return res.json({
              cart: cartResult,
              warning: "Cart created but line item could not be added.",
            });
          }
        }
      } catch (cartError) {
        console.error("Error creating cart:", cartError);
        throw cartError;
      }
    } catch (orderError) {
      console.error("Error creating booking cart:", orderError);
      return res.status(400).json({
        message: "Failed to create booking cart",
        error:
          orderError instanceof Error ? orderError.message : "Unknown error",
      });
    }
  } catch (error) {
    console.error("Error in booking cart creation:", error);

    // Provide more detailed error information
    return res.status(500).json({
      message: "Failed to create booking cart",
      error: error instanceof Error ? error.message : "Unknown error",
      stack:
        process.env.NODE_ENV !== "production" && error instanceof Error
          ? error.stack
          : undefined,
    });
  }
}

/**
 * Update room inventory status from cart_reserved to reserved_unassigned
 * @param scope - The request scope
 * @param cartId - The cart ID
 * @param orderId - The order ID
 */
async function updateRoomInventoryAfterCartCompletion(
  scope: any,
  cartId: string,
  orderId: string
) {
  try {
    // Resolve necessary services
    const cartService = scope.resolve(Modules.CART);
    const roomInventoryService = scope.resolve("roomInventoryService");

    // Retrieve the cart to get reservation information
    const cart = await cartService.retrieveCart(cartId);

    // Check if the cart has room reservations in metadata
    if (
      !cart.metadata?.reservations ||
      !Array.isArray(cart.metadata.reservations)
    ) {
      console.log(`No room reservations found in cart ${cartId} metadata`);
      return;
    }

    console.log(
      `Updating room inventory status for cart ${cartId} to order ${orderId}`
    );

    // Update each reservation from cart_reserved to reserved_unassigned
    const reservations = cart.metadata.reservations;
    const updatedReservations = [];

    for (const reservation of reservations) {
      // Skip if missing required fields
      if (
        !reservation.room_id ||
        !reservation.from_date ||
        !reservation.to_date
      ) {
        console.warn(
          "Skipping reservation update due to missing required fields:",
          reservation
        );
        continue;
      }

      // Convert dates if needed
      const fromDate = new Date(reservation.from_date);
      const toDate = new Date(reservation.to_date);

      // Update the inventory status to reserved_unassigned and set order_id
      const updatedEntries = await roomInventoryService.updateInventoryStatus(
        reservation.room_id,
        fromDate,
        toDate,
        RoomInventoryStatus.RESERVED_UNASSIGNED,
        `Booking confirmed: Cart ${cartId} converted to Order ${orderId}`,
        null, // bookingInfo
        null, // expiresAt
        orderId, // Set the order_id
        null // Clear the cart_id since it's now an order
      );

      // Store the updated entries
      if (updatedEntries && updatedEntries.length > 0) {
        updatedReservations.push(
          ...updatedEntries.map((entry: any) => ({
            id: entry.id,
            room_id: entry.inventory_item_id,
            from_date: entry.from_date,
            to_date: entry.to_date,
            status: entry.status,
          }))
        );
      }
    }

    console.log(
      `Updated ${updatedReservations.length} room inventory entries to reserved_unassigned`
    );
    return updatedReservations;
  } catch (error) {
    console.error(
      "Error updating room inventory after cart completion:",
      error
    );
    // Don't throw the error, just log it to prevent blocking the cart completion
    return null;
  }
}

/**
 * Complete a booking by converting cart to order
 * Note: Payment session must be created and authorized before calling this endpoint
 * Use the /store/hotel-management/cart/payment endpoint to create and authorize payment sessions
 */
export async function PUT(req: MedusaRequest, res: MedusaResponse) {
  try {
    // Check if req.scope exists
    if (!req.scope) {
      return res.status(400).json({
        message: "Scope is not available, cannot complete cart",
      });
    }

    // Extract data from request body
    const { cart_id } = req.body as Record<string, any>;

    // Validate required fields
    if (!cart_id) {
      return res.status(400).json({
        message: "Missing required field: cart_id",
      });
    }

    // Complete the cart using workflow
    let completeResult: any;
    try {
      const { result } = await completeCartWorkflow(req.scope).run({
        input: {
          id: cart_id, // The workflow expects 'id' not 'cart_id'
        },
      });
      completeResult = result;

      // After successful cart completion, update room inventory status
      if (completeResult && completeResult.id) {
        await updateRoomInventoryAfterCartCompletion(
          req.scope,
          cart_id,
          completeResult.id
        );
      }
    } catch (e) {
      console.error("Error completing cart:", e);
      throw new Error("Failed to complete cart: Result is invalid");
    }

    return res.json({
      order: completeResult,
      success: true,
    });
  } catch (error) {
    // Log the error for server-side debugging
    console.error("Error in cart completion:", error);

    return res.status(500).json({
      message: "Failed to complete cart",
      error: "An unexpected error occurred. Please try again later.",
    });
  }
}
