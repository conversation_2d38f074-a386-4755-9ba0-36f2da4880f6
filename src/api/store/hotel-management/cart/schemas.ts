import { z } from "zod";

// Schema for individual traveler/guest details
export const TravelerSchema = z.object({
  name: z.string().min(1, "Name is required"),
  age: z.number().optional(), // Age is optional for adults, required for children/infants
});

// Schema for travelers metadata
export const TravelersMetadataSchema = z.object({
  adults: z.array(TravelerSchema).default([]),
  children: z.array(TravelerSchema).default([]),
  infants: z.array(TravelerSchema).default([]),
});

// Main cart creation schema
export const StoreCartCreationSchema = z
  .object({
    hotel_id: z.string().min(1, "Hotel ID is required"),
    room_id: z.string().optional(),
    room_config_id: z.string().min(1, "Room configuration ID is required"),
    room_type: z.string().optional(),
    check_in_date: z.string().min(1, "Check-in date is required"),
    check_out_date: z.string().min(1, "Check-out date is required"),
    check_in_time: z.string().default("12:00"),
    check_out_time: z.string().default("12:00"),

    // Primary guest information (backward compatibility)
    guest_name: z.string().optional(),
    guest_email: z.string().email("Valid email is required"),
    guest_phone: z.string().optional(),

    // Guest counts
    adults: z.number().min(1, "At least 1 adult is required").default(1),
    children: z.number().min(0).default(0),
    infants: z.number().min(0).default(0),

    // Multiple guest details (new feature)
    travelers: TravelersMetadataSchema.optional(),

    // Booking details
    number_of_rooms: z
      .number()
      .min(1, "At least 1 room is required")
      .default(1),
    total_amount: z.number().min(0, "Total amount must be positive"),
    currency_code: z.string().min(1, "Currency code is required"),
    region_id: z.string().min(1, "Region ID is required"),
    country_code: z.string().optional(),

    // Additional information
    special_requests: z.string().optional(),
    notes: z.string().optional(),
    metadata: z.record(z.any()).optional(),

    // Address information
    shipping_address: z
      .object({
        first_name: z.string().optional(),
        last_name: z.string().optional(),
        address_1: z.string().optional(),
        city: z.string().optional(),
        country_code: z.string().optional(),
        postal_code: z.string().optional(),
        phone: z.string().optional(),
      })
      .optional(),
    billing_address: z
      .object({
        first_name: z.string().optional(),
        last_name: z.string().optional(),
        address_1: z.string().optional(),
        city: z.string().optional(),
        country_code: z.string().optional(),
        postal_code: z.string().optional(),
        phone: z.string().optional(),
      })
      .optional(),

    // Customer information
    customer_id: z.string().optional(),
    sales_channel_id: z.string().optional(),
  })
  .refine(
    (data) => {
      // If travelers are provided, validate that the counts match
      // Note: guest_name/guest_email represents one adult traveler for backward compatibility
      if (data.travelers) {
        const adultsCount = data.travelers.adults?.length || 0;
        const childrenCount = data.travelers.children?.length || 0;
        const infantsCount = data.travelers.infants?.length || 0;

        // guest_name/guest_email counts as 1 adult, so travelers.adults should be (total adults - 1)
        const expectedAdultsInTravelers = Math.max(0, data.adults - 1);

        return (
          adultsCount === expectedAdultsInTravelers &&
          childrenCount === data.children &&
          infantsCount === data.infants
        );
      }
      return true;
    },
    {
      message:
        "Traveler counts must match the number of traveler details provided. Note: guest_name/guest_email represents one adult traveler.",
    }
  );

export type StoreCartCreationType = z.infer<typeof StoreCartCreationSchema>;
export type TravelerType = z.infer<typeof TravelerSchema>;
export type TravelersMetadataType = z.infer<typeof TravelersMetadataSchema>;
