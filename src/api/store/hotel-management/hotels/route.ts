import {
  AuthenticatedMedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { ContainerRegistrationKeys } from "@camped-ai/framework/utils";

export const GET = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);
  const {
    limit = 20,
    offset = 0,
    is_featured,
    is_active,
    is_pets_allowed,
    searchParams,
  } = req.query || {};
  const filters: Record<string, any> = {};
  const params = searchParams && JSON.parse(searchParams as string);

  if (is_featured !== undefined) {
    filters.is_featured = is_featured === "true";
  }
  if (is_active !== undefined) {
    filters.is_active = is_active === "true";
  }
  if (is_pets_allowed !== undefined) {
    filters.is_pets_allowed = is_pets_allowed === "true";
  }
  if (params && params?.destination_id) {
    filters.destination_id = params?.destination_id;
  }
  const {
    data: hotels,
    metadata: { count, take, skip },
  } = await query.graph({
    entity: "hotel",
    filters,
    fields: ["*", "images.*", ...(req.validatedQuery?.fields.split(",") || [])],
    pagination: {
      skip: Number(offset),
      take: Number(limit),
    },
  });

  res.json({
    hotels,
    count,
    limit: take,
    offset: skip,
  });
};
