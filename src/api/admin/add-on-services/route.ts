import type { MedusaRequest, MedusaResponse } from "@camped-ai/framework";
import { z } from "zod";
import {
  ADD_ON_SERVICE,
  AddOnServiceType,
  AddOnServiceLevel,
} from "../../../modules/hotel-management/add-on-service";
import { registerAddOnServiceModule } from "../hotel-management/add-on-services/register-module";

// Validation schema for creating/updating an add-on service
const AddOnServiceSchema = z
  .object({
    name: z.string(),
    description: z.string().optional(),
    type: z.string().optional(),
    service_level: z.nativeEnum(AddOnServiceLevel),
    hotel_id: z.union([z.string(), z.array(z.string())]).optional(),
    destination_id: z.union([z.string(), z.array(z.string())]).optional(),
    is_active: z.boolean().default(true),
    start_date: z.string().optional(),
    end_date: z.string().optional(),
    max_capacity: z
      .number()
      .nullable()
      .optional()
      .transform((val) => (val === null ? 999999 : val)),

    adult_price: z.number().optional(),
    child_price: z.number().optional(),
    currency_code: z.string().optional(),
    images: z.array(z.string()).optional(),
    metadata: z.record(z.any()).optional(),
  })
  .refine(
    (data) => {
      // Ensure service_level matches the provided ID
      if (data.service_level === AddOnServiceLevel.HOTEL) {
        return !!data.hotel_id;
      } else if (data.service_level === AddOnServiceLevel.DESTINATION) {
        return !!data.destination_id;
      }
      return false;
    },
    {
      message:
        "hotel_id is required for HOTEL services, destination_id is required for DESTINATION services",
      path: ["service_level"],
    }
  );

// GET /admin/add-on-services
export async function GET(
  req: MedusaRequest,
  res: MedusaResponse
): Promise<void> {
  try {
    // Register the service module
    registerAddOnServiceModule(req.scope);

    // Get the service
    const addOnServiceService: any = req.scope.resolve(ADD_ON_SERVICE);

    // Parse query parameters
    const { hotel_id, type, is_active, limit, offset } = req.query;
    const service_level = req.query.service_level as string;
    const destination_id = req.query.destination_id as string;
    const destination_ids = req.query.destination_ids as string;

    // Log destination IDs for debugging
    if (destination_ids) {
      console.log(`Received destination_ids in request: ${destination_ids}`);
    }

    // Build selector
    const selector: Record<string, any> = {};

    // Add filters if they're valid values
    if (
      service_level &&
      service_level !== "undefined" &&
      service_level !== "null"
    ) {
      selector.service_level = service_level;
    }

    if (hotel_id && hotel_id !== "undefined" && hotel_id !== "null") {
      selector.hotel_id = hotel_id;
    }

    if (
      destination_id &&
      destination_id !== "undefined" &&
      destination_id !== "null"
    ) {
      selector.destination_id = destination_id;
    }

    if (type) selector.type = type;
    if (is_active !== undefined) selector.is_active = is_active === "true";

    // Build config
    const config = {
      skip: parseInt(offset as string) || 0,
      take: parseInt(limit as string) || 20,
    };

    // Prepare additional options
    const options: Record<string, any> = {};

    // If destination_ids is provided, pass it to the service
    if (destination_ids) {
      options.destination_ids = destination_ids.split(",");
    }

    // Get add-on services
    const result = await addOnServiceService.listAddOnServices(
      selector,
      config,
      options
    );

    // Process the result
    let addOnServices = [];
    let count = 0;

    if (Array.isArray(result)) {
      // Handle array response [services, count]
      addOnServices = result[0] || [];
      count = result[1] || 0;
    } else if (result && typeof result === "object") {
      // Handle object response {data, count}
      addOnServices = result.data || [];
      count = result.count || 0;
    }

    // Return the response
    res.json({
      add_on_services: addOnServices,
      count,
    });
  } catch (error) {
    console.error("Error listing add-on services:", error);
    res.status(500).json({
      message: "Failed to list add-on services",
      error: error.message,
    });
  }
}

// POST /admin/add-on-services
export async function POST(
  req: MedusaRequest,
  res: MedusaResponse
): Promise<void> {
  try {
    // Register the service module
    registerAddOnServiceModule(req.scope);

    // Get the service
    const addOnServiceService: any = req.scope.resolve(ADD_ON_SERVICE);

    // Validate request body
    let validatedData: any;
    try {
      validatedData = AddOnServiceSchema.parse(req.body);

      // Handle destination_id - convert array to string if needed for API compatibility
      if (
        validatedData.service_level === AddOnServiceLevel.DESTINATION &&
        Array.isArray(validatedData.destination_id) &&
        validatedData.destination_id.length > 0
      ) {
        console.log(
          `API received destination_id as array:`,
          validatedData.destination_id
        );

        // Keep the array format in the API
        console.log(`Keeping destination_id as array for service module`);
      }
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          message: "Validation error",
          errors: error.errors,
        });
      }
      throw error;
    }

    // Transform max_capacity if it's null
    if (validatedData.max_capacity === null) {
      validatedData.max_capacity = 999999;
    }

    // Handle hotel_id - keep as array for multi-select functionality
    if (
      validatedData.service_level === AddOnServiceLevel.HOTEL &&
      Array.isArray(validatedData.hotel_id) &&
      validatedData.hotel_id.length > 0
    ) {
      console.log(`API received hotel_id as array:`, validatedData.hotel_id);
      console.log(`Keeping hotel_id as array for service module`);
    }

    // Log the data being sent to the service
    console.log("Creating add-on service with data:", {
      name: validatedData.name,
      service_level: validatedData.service_level,
      hotel_id: validatedData.hotel_id,
      destination_id: validatedData.destination_id,
    });

    // Create add-on service
    const addOnService = await addOnServiceService.createAddOnService(
      validatedData
    );
    res.status(201).json({
      add_on_service: addOnService,
    });
  } catch (error) {
    console.error("Error creating add-on service:", error);

    if (error instanceof z.ZodError) {
      res.status(400).json({
        message: "Validation error",
        errors: error.errors,
      });
    } else {
      res.status(500).json({
        message: "Failed to create add-on service",
        error: error.message,
      });
    }
  }
}
