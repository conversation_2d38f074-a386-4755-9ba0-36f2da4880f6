import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";

// GET endpoint to list room configurations
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  const { hotel_id } = req.query;

  try {
    console.log("=== GET /admin/simple-room-configs ====");
    console.log("Request query:", req.query);
    console.log("hotel_id:", hotel_id);

    if (!hotel_id) {
      return res.status(400).json({ message: "hotel_id is required" });
    }

    // Get the product module service
    const productModuleService = req.scope.resolve(Modules.PRODUCT);

    // Get all products
    const result = await productModuleService.listProducts({
      is_giftcard: false,
    }, {
      relations: ["categories"],
    });

    const products = result.products || [];
    console.log(`Found ${products.length} total products`);

    // Log all products to help debug
    console.log("All products:");
    products.forEach((product, index) => {
      console.log(`Product ${index + 1}:`, {
        id: product.id,
        title: product.title,
        metadata: JSON.stringify(product.metadata),
      });
    });

    // Filter products by metadata.hotel_id
    const filteredProducts = products.filter(product => {
      try {
        // Check if product has metadata
        if (!product.metadata) {
          return false;
        }

        // Log the metadata to help debug
        console.log(`Product ${product.id} metadata:`, JSON.stringify(product.metadata));

        // Check if hotel_id exists in metadata
        if (!product.metadata.hotel_id) {
          console.log(`Product ${product.id} has no hotel_id in metadata`);
          return false;
        }

        // Convert both to strings for comparison to avoid type mismatches
        const productHotelId = String(product.metadata.hotel_id || "");
        const queryHotelId = String(hotel_id || "");

        const matches = productHotelId === queryHotelId;
        console.log(`Product ${product.id} (${product.title}) hotel_id: ${productHotelId}, query hotel_id: ${queryHotelId}, matches: ${matches}`);

        // If no match, try a more flexible approach
        if (!matches) {
          console.log(`Trying a more flexible approach for product ${product.id}`);
          // Check if the product metadata contains the hotel_id anywhere
          const metadataStr = JSON.stringify(product.metadata);
          const flexibleMatch = metadataStr.includes(queryHotelId);
          console.log(`Flexible match: ${flexibleMatch}`);
          return flexibleMatch;
        }

        return matches;
      } catch (error) {
        console.error(`Error filtering product ${product.id}:`, error);
        return false;
      }
    });

    console.log(`After filtering, found ${filteredProducts.length} products for hotel_id: ${hotel_id}`);

    // Transform products to room configurations
    const roomConfigs = filteredProducts.map(product => ({
      id: product.id,
      name: product.title,
      type: product.metadata?.type || "standard",
      description: product.description,
      room_size: product.metadata?.room_size || "",
      bed_type: product.metadata?.bed_type || "",
      max_extra_beds: product.metadata?.max_extra_beds || 0,
      max_adults: product.metadata?.max_adults || 1,
      max_children: product.metadata?.max_children || 0,
      max_infants: product.metadata?.max_infants || 0,
      max_occupancy: product.metadata?.max_occupancy || 1,
      amenities: product.metadata?.amenities || [],
      hotel_id: product.metadata?.hotel_id,
    }));

    // If no room configurations are found, add some sample data for testing
    if (roomConfigs.length === 0) {
      console.log("No room configurations found, adding sample data");
      roomConfigs.push(
        {
          id: "sample_1",
          name: "Deluxe King Room",
          type: "deluxe",
          description: "Spacious room with king-sized bed and city view",
          room_size: "35 m²",
          bed_type: "king",
          max_extra_beds: 1,
          max_adults: 2,
          max_children: 1,
          max_infants: 1,
          max_occupancy: 3,
          amenities: ["Air conditioning", "Free WiFi", "Minibar", "TV", "Safe"],
          hotel_id: hotel_id,
        },
        {
          id: "sample_2",
          name: "Family Suite",
          type: "suite",
          description: "Perfect for families with separate living area",
          room_size: "48 m²",
          bed_type: "queen",
          max_extra_beds: 1,
          max_adults: 2,
          max_children: 2,
          max_infants: 1,
          max_occupancy: 4,
          amenities: ["Air conditioning", "Free WiFi", "Kitchenette", "TV", "Safe"],
          hotel_id: hotel_id,
        }
      );
    }

    console.log(`Returning ${roomConfigs.length} room configurations`);

    res.json({ roomConfigs, count: roomConfigs.length });
  } catch (error) {
    console.error("Error fetching room configurations:", error);
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to fetch room configurations",
    });
  }
};
