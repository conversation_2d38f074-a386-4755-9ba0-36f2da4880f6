import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";
import { NOTIFICATION_TEMPLATE_SERVICE } from "src/modules/notification-template/service";
import NotificationTemplateService from "src/modules/notification-template/service";
import * as pdf from "html-pdf";

// Function to replace placeholders in a template string
const replacePlaceholders = (
  template: string,
  data: Record<string, any>
): string => {
  // First, normalize the template by removing line breaks within placeholders
  // This approach handles multi-line placeholders by first finding all placeholders
  // and then replacing any line breaks within them

  // Function to normalize placeholders by removing line breaks
  const normalizePlaceholder = (placeholder: string): string => {
    // Remove all line breaks and extra spaces within the placeholder
    return placeholder
      .replace(/\s*\n\s*/g, " ")
      .replace(/\s+/g, " ")
      .trim();
  };

  // Find all double-brace placeholders and normalize them
  let normalizedTemplate = template.replace(/{{\s*[^{}]*?}}/g, (match) =>
    normalizePlaceholder(match)
  );

  // Find all triple-brace placeholders and normalize them
  normalizedTemplate = normalizedTemplate.replace(
    /{{{\s*[^{}]*?}}}/g,
    (match) => normalizePlaceholder(match)
  );

  let result = normalizedTemplate;

  // First, handle default values with the pipe syntax: {{ value | default: 'default value' }} or {{ value | default: ''default value'' }}
  // This regex handles both single quotes, double quotes, and double single quotes for default values
  const defaultValueRegex =
    /{{\s*([a-zA-Z0-9_.]+)\s*\|\s*default:\s*(?:['"]([^'"]*)['"]|''([^']*)'')\s*}}/g;
  result = result.replace(
    defaultValueRegex,
    (match, path, defaultValue1, defaultValue2) => {
      // Use the appropriate default value (either from single/double quotes or double single quotes)
      const defaultValue = defaultValue1 || defaultValue2 || "";

      // Split the path into parts (e.g., "company.name" -> ["company", "name"])
      const parts = path.trim().split(".");

      // Navigate through the data object following the path
      let value = data;
      for (const part of parts) {
        value = value?.[part];
        if (value === undefined || value === null) {
          return defaultValue; // Return default value if path doesn't exist
        }
      }

      return value.toString();
    }
  );

  // Then handle regular placeholders without default values
  for (const key in data) {
    // Handle nested objects for simple dot notation like order.customer.email
    if (typeof data[key] === "object" && data[key] !== null) {
      for (const nestedKey in data[key]) {
        const placeholder = new RegExp(`{{\s*${key}.${nestedKey}\s*}}`, "g");
        result = result.replace(
          placeholder,
          data[key][nestedKey]?.toString() || ""
        );
      }
    } else {
      const placeholder = new RegExp(`{{\s*${key}\s*}}`, "g");
      result = result.replace(placeholder, data[key]?.toString() || "");
    }
  }

  // Regex for item blocks
  if (data.items_html_block) {
    result = result.replace(
      /{{{\s*items_html_block\s*}}}/g,
      data.items_html_block
    );
  }

  return result;
};

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  const { id: bookingId } = req.params;
  const INVOICE_TEMPLATE_EVENT_NAME = "booking.invoice.default"; // Or your chosen event name

  if (!bookingId) {
    return res.status(400).json({ message: "Booking ID is required." });
  }

  const orderService: any = req.scope.resolve(Modules.ORDER);
  const notificationTemplateService: NotificationTemplateService =
    req.scope.resolve(NOTIFICATION_TEMPLATE_SERVICE);

  try {
    // 1. Fetch the Order
    const order = await orderService.retrieveOrder(bookingId, {
      relations: ["billing_address", "items"],
    });

    if (!order) {
      return res.status(404).json({ message: "Booking not found." });
    }

    // 2. Fetch the Invoice Template (Corrected Logic)
    if (
      !notificationTemplateService ||
      typeof notificationTemplateService.listNotificationTemplates !==
        "function"
    ) {
      console.error(
        "NotificationTemplateService or listNotificationTemplates method not found."
      );
      const basicHtml = `<html><body><h1>Invoice Error</h1><p>Template service unavailable.</p><p>Order ID: ${
        order.display_id || order.id
      }</p></body></html>`;
      pdf.create(basicHtml).toBuffer((err, buffer) => {
        if (err) {
          console.error("Error generating basic fallback PDF:", err);
          return res
            .status(500)
            .json({ message: "Fallback PDF generation failed." });
        }
        res.setHeader("Content-Type", "application/pdf");
        res.setHeader(
          "Content-Disposition",
          `attachment; filename="invoice-error.pdf"`
        );
        res.send(buffer);
      });
      return;
    }

    let invoiceTemplate = null; // Initialize as null
    try {
      const filters = {
        event_name: INVOICE_TEMPLATE_EVENT_NAME,
        is_active: true,
        channel: "pdf",
      };
      const config = { take: 1 }; // We only need one template

      // Use listNotificationTemplates as seen in the other route file
      const templates =
        await notificationTemplateService.listNotificationTemplates(
          filters,
          config
        );

      if (templates && templates.length > 0) {
        invoiceTemplate = templates[0];
        console.info(`Found invoice template: ${invoiceTemplate.id}`); // Added log
      } else {
        console.warn(
          `No active PDF template found for event: ${INVOICE_TEMPLATE_EVENT_NAME}`
        ); // Added log
      }
    } catch (templateError: any) {
      console.error(
        `Error fetching invoice template '${INVOICE_TEMPLATE_EVENT_NAME}':`,
        templateError
      );
      // Consider a fallback or re-throw if template is critical
    }

    // Check if template was found *after* the try-catch
    if (!invoiceTemplate || !invoiceTemplate.content) {
      console.error(
        `Invoice template '${INVOICE_TEMPLATE_EVENT_NAME}' ultimately not loaded or has no content.`
      );
      const fallbackHtml = `<html><body><h1>Invoice</h1><p>Order ID: ${
        order.display_id || order.id
      }</p><p>Error: Invoice template '${INVOICE_TEMPLATE_EVENT_NAME}' not configured or inactive.</p></body></html>`;
      pdf.create(fallbackHtml, {}).toBuffer((err, buffer) => {
        if (err) {
          console.error("Error generating fallback PDF:", err);
          return res
            .status(500)
            .json({ message: "Fallback PDF generation failed." });
        }
        res.setHeader("Content-Type", "application/pdf");
        res.setHeader(
          "Content-Disposition",
          `attachment; filename="invoice-error.pdf"`
        );
        res.send(buffer);
      });
      return;
    }

    // 3. Prepare Data and Formatters for Placeholders
    const currencyCode = order.currency_code.toUpperCase();
    const dateFormatter = (dateStr: string | Date) =>
      dateStr
        ? new Date(dateStr).toLocaleDateString(undefined, {
            year: "numeric",
            month: "short",
            day: "numeric",
          })
        : "N/A";
    const currencyFormatter = (amountValue: string | number | unknown) => {
      const amount = parseFloat(String(amountValue));
      if (isNaN(amount)) return `N/A`;
      try {
        return new Intl.NumberFormat(undefined, {
          style: "currency",
          currency: currencyCode,
        }).format(amount / 100);
      } catch (e) {
        return `${(amount / 100).toFixed(2)} ${currencyCode}`;
      }
    };

    // Create a data context for placeholder replacement
    const dataContext: Record<string, any> = {
      order: {
        display_id: order.display_id || order.id,
        created_at_formatted: dateFormatter(order.created_at),
        subtotal_formatted: currencyFormatter(order.subtotal),
        shipping_total_formatted: currencyFormatter(order.shipping_total),
        tax_total_formatted: currencyFormatter(order.tax_total),
        total_formatted: currencyFormatter(order.total),
        currency_code: order.currency_code,
        notes: order.notes || "",
        payment_method_name: order.payment_method_name || "N/A",
      },
      customer: {
        email: order.customer?.email || order.email || "N/A",
        first_name: order.customer?.first_name || "",
        last_name: order.customer?.last_name || "",
      },
      billing_address: {
        address_1: order.billing_address?.address_1 || "",
        address_2: order.billing_address?.address_2 || "",
        city: order.billing_address?.city || "",
        province: order.billing_address?.province || "",
        postal_code: order.billing_address?.postal_code || "",
        country_code: order.billing_address?.country_code?.toUpperCase() || "",
        full_address_html: order.billing_address
          ? `${order.billing_address.address_1 || ""}<br>${
              order.billing_address.address_2 || ""
            }<br>${order.billing_address.city || ""}, ${
              order.billing_address.province || ""
            } ${order.billing_address.postal_code || ""}<br>${
              order.billing_address.country_code?.toUpperCase() || ""
            }`
              .replace(/<br><br>/g, "<br>")
              .trim()
          : "N/A",
      },
      // Placeholder for a block of HTML for items - generated without variant info
      items_html_block: order.items
        .map(
          (item: any) =>
            `<tr>
          <td>${item.title || "N/A"}</td>
          <td>${"N/A" /* item.variant?.title || 'N/A' */}</td>
          <td style="text-align: right;">${
            item.quantity?.toString() || "1"
          }</td>
          <td style="text-align: right;">${currencyFormatter(
            item.unit_price
          )}</td>
          <td style="text-align: right;">${currencyFormatter(
            item.total || parseFloat(String(item.unit_price)) * item.quantity
          )}</td>
        </tr>`
        )
        .join(""),
    };

    // 4. Replace placeholders in the template content
    const processedHtmlContent = replacePlaceholders(
      invoiceTemplate.content,
      dataContext
    );

    const options: pdf.CreateOptions = {
      format: "A4",
      orientation: "portrait",
      border: { top: "0.5in", right: "0.5in", bottom: "0.5in", left: "0.5in" },
    };

    pdf.create(processedHtmlContent, options).toBuffer((err, buffer) => {
      if (err) {
        console.error("Error generating PDF with html-pdf:", err);
        return res.status(500).json({
          message: "Failed to generate PDF.",
          errorDetail: err.message,
        });
      }
      res.setHeader("Content-Type", "application/pdf");
      res.setHeader(
        "Content-Disposition",
        `attachment; filename="invoice-${order.display_id || order.id}.pdf"`
      );
      res.send(buffer);
    });
  } catch (error: any) {
    console.error("Error in GET /invoice route:", error);
    let statusCode = 500;
    let message = "Failed to process invoice request.";
    // Check specifically for the strategy error if possible (may need to inspect error properties)
    if (error.message?.includes("'strategy'")) {
      message =
        "Failed to process order relations. Please check ORM configuration.";
      // Optionally provide more specific feedback or log details differently
    } else if (
      error.message?.toLowerCase().includes("not found") ||
      error.name === "NotFoundError" ||
      error.type === "not_found"
    ) {
      statusCode = 404;
      message = error.message || "Booking or related data not found.";
    }
    res.status(statusCode).json({ message, errorDetail: error.message });
  }
}
