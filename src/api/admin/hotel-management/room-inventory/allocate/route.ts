import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";
import { z } from "zod";

// Validation schema for allocating a room
export const AllocateRoomSchema = z.object({
  inventory_id: z.string().optional(),
  room_id: z.string(),
  order_id: z.string(),
  from_date: z.string().or(z.date()),
  to_date: z.string().or(z.date()),
  status: z.string().default("reserved_unassigned"),
});

export type AllocateRoomType = z.infer<typeof AllocateRoomSchema>;

/**
 * Endpoint to allocate a room to an order
 * This adds the reservation to the order metadata AND sets the order_id in room_inventory
 *
 * @param req - The request object
 * @param res - The response object
 * @returns Success status and updated order
 */
export const POST = async (
  req: MedusaRequest<AllocateRoomType>,
  res: MedusaResponse
) => {
  try {
    // Validate request body
    const validationResult = AllocateRoomSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({
        message: "Invalid request body",
        errors: validationResult.error.format(),
      });
    }

    const { room_id, order_id, from_date, to_date, status, inventory_id } =
      validationResult.data;

    // Get services
    const orderService = req.scope.resolve(Modules.ORDER);
    const roomInventoryService = req.scope.resolve("roomInventoryService");

    // Get the order
    const order = await orderService.retrieveOrder(order_id);
    if (!order) {
      return res.status(404).json({
        message: "Order not found",
      });
    }

    // Check if order has reservations in metadata
    if (
      !order.metadata?.reservations ||
      !Array.isArray(order.metadata.reservations)
    ) {
      // If no reservations array exists, create one
      order.metadata = {
        ...order.metadata,
        reservations: [],
      };
    }

    // Create a new reservation
    const newReservation = {
      room_id,
      from_date: new Date(from_date),
      to_date: new Date(to_date),
      status: status || "reserved_unassigned",
      id: inventory_id || `reservation_${Date.now()}`,
    };

    // Add the reservation to the order metadata
    const updatedReservations = [
      ...(Array.isArray(order.metadata.reservations)
        ? order.metadata.reservations
        : []),
      newReservation,
    ];

    console.log({ updatedReservations });

    // Update the order metadata
    const updatedMetadata = {
      ...order.metadata,
      reservations: updatedReservations,
    };

    // Update the order
    await orderService.updateOrders(order_id, {
      metadata: updatedMetadata,
    });

    // Use the updateInventoryStatus function to update the room inventory
    const bookingInfo = {
      order_id,
      reservation_id: newReservation.id,
    };

    try {
      // Use the service function to update inventory status
      const updatedEntries = await roomInventoryService.updateInventoryStatus(
        room_id,
        new Date(from_date),
        new Date(to_date),
        status || "reserved_unassigned",
        `Allocated to order ${order_id} on ${new Date().toISOString()}`,
        bookingInfo,
        null, // expiresAt
        order_id, // orderId
        null // cartId
      );

      if (updatedEntries && updatedEntries.length > 0) {
        console.log(
          `Successfully updated inventory status for room ${room_id} from ${from_date} to ${to_date}`
        );
      } else {
        console.log(
          `No inventory entries found for room ${room_id} from ${from_date} to ${to_date}, no updates made`
        );
      }
    } catch (inventoryError) {
      console.error("Error updating room inventory:", inventoryError);
      // We don't throw here as the order metadata was already updated
      // Instead, we log the error and continue
    }

    // Get the updated order
    const updatedOrder = await orderService.retrieveOrder(order_id);

    return res.json({
      success: true,
      message: "Room allocated successfully",
      order: updatedOrder,
    });
  } catch (error) {
    console.error("Error allocating room:", error);
    return res.status(500).json({
      message: "Error allocating room",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
