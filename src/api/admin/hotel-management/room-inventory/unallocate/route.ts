import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";
import { z } from "zod";

// Validation schema for unallocating a room
export const UnallocateRoomSchema = z.object({
  inventory_id: z.string().optional(),
  room_id: z.string(),
  order_id: z.string(),
  from_date: z.string().or(z.date()),
  to_date: z.string().or(z.date()),
});

export type UnallocateRoomType = z.infer<typeof UnallocateRoomSchema>;

/**
 * Endpoint to unallocate a room from an order
 * This removes the reservation from the order metadata AND removes the order_id from room_inventory
 *
 * @param req - The request object
 * @param res - The response object
 * @returns Success status and updated order
 */
export const POST = async (
  req: MedusaRequest<UnallocateRoomType>,
  res: MedusaResponse
) => {
  try {
    // Validate request body
    const validationResult = UnallocateRoomSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({
        message: "Invalid request body",
        errors: validationResult.error.format(),
      });
    }

    const { room_id, order_id, from_date, to_date, inventory_id } =
      validationResult.data;

    // Get services
    const orderService = req.scope.resolve(Modules.ORDER);
    const roomInventoryService = req.scope.resolve("roomInventoryService");
    const query = req.scope.resolve("query");

    // Get the order
    const order = await orderService.retrieveOrder(order_id);
    if (!order) {
      return res.status(404).json({
        message: "Order not found",
      });
    }

    console.log({ order });

    // Check if order has reservations in metadata
    if (
      !order.metadata?.reservations ||
      !Array.isArray(order.metadata.reservations)
    ) {
      return res.status(400).json({
        message: "Order does not have reservation information",
      });
    }

    // Find the reservation to unallocate
    const reservations = order.metadata.reservations;
    console.log({ reservations });
    const reservationIndex = reservations.findIndex(
      (r) =>
        r.room_id === room_id &&
        r.from_date === from_date &&
        r.to_date === to_date
    );

    // Remove the reservation from the order metadata
    const updatedReservations = [...reservations];
    updatedReservations.splice(reservationIndex, 1);

    // Update the order metadata
    const updatedMetadata = {
      ...order.metadata,
      reservations: updatedReservations,
    };

    // Update the order
    await orderService.updateOrders(order_id, {
      metadata: updatedMetadata,
    });

    // Find the room inventory records for this room and date range
    const { data: roomInventoryRecords } = await query.graph({
      entity: "room_inventory",
      filters: {
        inventory_item_id: [room_id],
        order_id: [order_id],
        from_date: { $lte: new Date(to_date) },
        to_date: { $gte: new Date(from_date) },
      },
      fields: [
        "id",
        "inventory_item_id",
        "from_date",
        "to_date",
        "status",
        "order_id",
      ],
    });

    // Update each room inventory record to remove the order_id
    if (roomInventoryRecords && roomInventoryRecords.length > 0) {
      for (const record of roomInventoryRecords) {
        await roomInventoryService.updateRoomInventories({
          id: record.id,
          order_id: null,
          status: "available",
          available_quantity: 1,
          notes: `Unallocated from order ${order_id} on ${new Date().toISOString()}`,
        });
      }
    }

    // Get the updated order
    const updatedOrder = await orderService.retrieveOrder(order_id);

    return res.json({
      success: true,
      message: "Room unallocated successfully",
      order: updatedOrder,
    });
  } catch (error) {
    console.error("Error unallocating room:", error);
    return res.status(500).json({
      message: "Error unallocating room",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
