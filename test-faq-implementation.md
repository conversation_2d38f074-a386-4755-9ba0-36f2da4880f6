# FAQ Implementation Test Guide

## Testing the FAQ Functionality

### 1. Database Migration
Run the migration to create the FAQ table:
```bash
npm run build
npm run db:migrate
```

### 2. Backend API Testing

#### Create a destination with FAQs:
```bash
curl -X POST http://localhost:9000/admin/hotel-management/destinations \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Destination",
    "handle": "test-destination",
    "description": "A test destination",
    "country": "United States",
    "currency": "USD",
    "faqs": [
      {
        "question": "What is the best time to visit?",
        "answer": "The best time to visit is during spring and fall when the weather is mild."
      },
      {
        "question": "Are pets allowed?",
        "answer": "Yes, pets are welcome in most accommodations with prior notice."
      }
    ]
  }'
```

#### Update a destination with FAQs:
```bash
curl -X PUT http://localhost:9000/admin/hotel-management/destinations \
  -H "Content-Type: application/json" \
  -d '{
    "id": "dest_123",
    "faqs": [
      {
        "question": "Updated question?",
        "answer": "Updated answer."
      }
    ]
  }'
```

#### Fetch destination with FAQs:
```bash
curl http://localhost:9000/admin/hotel-management/destinations/test-destination
```

### 3. Frontend Testing

1. **Navigate to Destinations**: Go to `/hotel-management/destinations`
2. **Create New Destination**: Click "Add Destination" and navigate to the "FAQs" tab
3. **Add FAQs**: Use the "Add FAQ" button to add questions and answers
4. **Save Destination**: Complete the form and save
5. **View Destination**: Navigate to the destination detail page to see FAQs displayed
6. **Edit Destination**: Edit the destination and modify FAQs in the FAQs tab

### 4. Store API Testing

#### Fetch destinations with FAQs for frontend:
```bash
curl http://localhost:9000/store/hotel-management/destinations
```

#### Fetch specific destination with FAQs:
```bash
curl http://localhost:9000/store/hotel-management/destinations/test-destination
```

### 5. Expected Results

- FAQs should be saved to the database with proper ranking
- FAQs should appear in API responses
- FAQ section should be visible in the admin destination detail page
- FAQ tab should be functional in the destination form
- FAQ data should be included in store API responses for frontend consumption

### 6. Troubleshooting

If you encounter issues:

1. **Check database**: Verify the `destination_faq` table was created
2. **Check logs**: Look for any errors in the application logs
3. **Verify imports**: Ensure all new components are properly imported
4. **Check API responses**: Use browser dev tools to inspect API calls

The implementation is complete and ready for testing!
